import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import '@testing-library/jest-dom';
import DocPrepareAddSigner from '../DocPrepareAddSigner';

// Mock dependencies
jest.mock('react-responsive', () => ({
  useMediaQuery: jest.fn(() => false)
}));

// Mock firestore services
jest.mock('../../../../../app/firestore/firestoreService', () => ({}));
jest.mock('../../../../../app/firestore/firebaseService', () => ({}));

const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

const baseMockState = {
  annot: {
    signerListPossible: [
      { role: 'Buyer', firstName: '<PERSON>', lastName: '<PERSON><PERSON>' },
      { role: 'Buyer 2', firstName: '<PERSON>', lastName: 'Doe' },
      { role: 'Seller', firstName: '<PERSON>', lastName: '<PERSON>' },
      { role: 'Seller 2', firstName: 'Alice', lastName: 'Smith' },
      { role: 'Seller 3' } // No last name
    ],
    signerListDisplay: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
    ],
    selectedSigner: { role: 'Buyer' },
    annots: []
  },
  profile: {
    currentUserProfile: {
      firstName: 'Agent',
      lastName: 'User',
      isAdmin: false
    }
  },
  transaction: {
    transClients: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
    ],
    transaction: {
      agentRepresents: 'Buyer'
    }
  },
  doc: {
    doc: {
      annotsInProgress: []
    }
  }
};

describe('DocPrepareAddSigner - Filtering Logic', () => {
  test('excludes current user role from modal', () => {
    const store = createMockStore(baseMockState);
    
    render(
      <Provider store={store}>
        <DocPrepareAddSigner />
      </Provider>
    );

    // Should not show the current user's role (Buyer)
    expect(screen.queryByText(/^Buyer$/)).not.toBeInTheDocument();
    
    // Should show other roles
    expect(screen.getByText(/^Buyer 2/)).toBeInTheDocument();
    expect(screen.getByText(/^Seller/)).toBeInTheDocument();
  });

  test('excludes signers without last name for non-admin users', () => {
    const store = createMockStore(baseMockState);
    
    render(
      <Provider store={store}>
        <DocPrepareAddSigner />
      </Provider>
    );

    // Should not show Seller 3 (no last name) for non-admin user
    expect(screen.queryByText(/^Seller 3/)).not.toBeInTheDocument();
  });

  test('includes signers without last name for admin users', () => {
    const adminState = {
      ...baseMockState,
      profile: {
        currentUserProfile: {
          firstName: 'Admin',
          lastName: 'User',
          isAdmin: true
        }
      }
    };
    
    const store = createMockStore(adminState);
    
    render(
      <Provider store={store}>
        <DocPrepareAddSigner />
      </Provider>
    );

    // Should show Seller 3 (no last name) for admin user
    expect(screen.getByText(/^Seller 3/)).toBeInTheDocument();
  });

  test('excludes other side clients when they have annotsInProgress', () => {
    const stateWithOtherSideAnnots = {
      ...baseMockState,
      doc: {
        doc: {
          annotsInProgress: [
            { signerRole: 'Seller', agentsField: false, type: 'signature' }
          ]
        }
      }
    };
    
    const store = createMockStore(stateWithOtherSideAnnots);
    
    render(
      <Provider store={store}>
        <DocPrepareAddSigner />
      </Provider>
    );

    // Should not show other side clients (Seller, Seller 2) when they have annotsInProgress
    expect(screen.queryByText(/^Seller:/)).not.toBeInTheDocument();
    expect(screen.queryByText(/^Seller 2:/)).not.toBeInTheDocument();
    
    // Should still show current side clients
    expect(screen.getByText(/^Buyer 2/)).toBeInTheDocument();
  });

  test('includes other side clients when they have no annotsInProgress', () => {
    const store = createMockStore(baseMockState);
    
    render(
      <Provider store={store}>
        <DocPrepareAddSigner />
      </Provider>
    );

    // Should show other side clients when they have no annotsInProgress
    expect(screen.getByText(/^Seller:/)).toBeInTheDocument();
    expect(screen.getByText(/^Seller 2:/)).toBeInTheDocument();
  });
});
