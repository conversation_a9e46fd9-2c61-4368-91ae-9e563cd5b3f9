import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Segment,
} from "semantic-ui-react";
import { closeModal } from "../../../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../../../app/common/modals/modalWrapper";
import { convertFullName } from "../../../../../app/common/util/util";
import { createSignerIndex } from "../annots/annotUtils";
import { roleIsOtherAgentsClients } from "../sendForSigning/sendForSigningUtils";
import {
  addToSignerList,
  changeSelectedSigner,
  removeAnnotsFromSigner,
  removeFromSignerList,
} from "../../../../../app/annots/annotSlice";

export default function DocPrepareAddSigner() {
  const dispatch = useDispatch();
  const { transClients } = useSelector((state) => state.transaction);
  const { signerListPossible, signerListDisplay, selectedSigner, annots } =
    useSelector((state) => state.annot);
  const { transaction } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { doc } = useSelector((state) => state.doc);

  function handleSelectSigner(signer) {
    if (signerListDisplay.find(({ role }) => role === signer.role)) {
      dispatch(removeFromSignerList(signer));
      if (annots.filter((annot) => annot.signerRole === signer.role)[0]) {
        dispatch(removeAnnotsFromSigner(signer));
      }
      if (selectedSigner.role === signer.role) {
        dispatch(changeSelectedSigner(transClients[0]));
      }
    } else {
      const newSignerIndex = createSignerIndex(signer.role, transaction);
      let signerCopy = JSON.parse(JSON.stringify(signer));
      dispatch(
        addToSignerList({
          signer: signerCopy,
          index: newSignerIndex,
        })
      );
    }
  }

  function handleDone() {
    dispatch(
      closeModal({
        modalType: "DocPrepareAddSigner",
      })
    );
  }

  // Check if there are annotsInProgress for other side clients
  const hasOtherSideAnnots =
    doc.annotsInProgress &&
    doc.annotsInProgress.some(
      (annot) =>
        roleIsOtherAgentsClients(annot.signerRole, transaction) &&
        !annot.agentsField
    );

  // Filter signers for the modal
  const filteredSigners = signerListPossible.filter((signer) => {
    // Exclude the current user's role (first client)
    if (signer.role === transClients[0].role) {
      return false;
    }

    // If there are annotsInProgress for other side, exclude other side clients from modal
    // (they should be shown in the main signer list instead)
    if (
      hasOtherSideAnnots &&
      roleIsOtherAgentsClients(signer.role, transaction)
    ) {
      return false;
    }

    // If no last name, only show if user is admin
    if (!signer.lastName && !currentUserProfile?.isAdmin) {
      return false;
    }

    return true;
  });

  return (
    <ModalWrapper>
      <Segment>
        <Grid>
          <Grid.Column width="16">
            <Header size="huge" color="blue">
              Edit Signer List
            </Header>
            <Divider />
            <p
              className="medium bottom margin"
              style={{ fontSize: "16px", color: "#888" }}
            >
              Check the people below that you want to add to your list of
              signers.&nbsp;&nbsp;Note: If you uncheck someone who was
              previously checked, any signing fields you already assigned them
              will be removed.
              <br />
              Make sure you have added the signers into Parties for them to show
              up here.
            </p>
            {filteredSigners.map((signer) => (
              <div
                key={signer.role}
                onClick={() => handleSelectSigner(signer)}
                className="small bottom margin"
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <Icon
                  className="small right margin"
                  name={
                    signerListDisplay.find(({ role }) => role === signer.role)
                      ? "square check outline"
                      : "square outline"
                  }
                  size="large"
                />
                <h3
                  className="zero top margin small left margin"
                  style={{ color: "#666" }}
                >
                  {signer.role}
                  {signer.lastName && ": " + convertFullName(signer)}
                </h3>
              </div>
            ))}
            <Divider className="tiny margin bottom" />
            <Button
              onClick={() => handleDone()}
              floated="right"
              primary
              content="Done"
            />
          </Grid.Column>
        </Grid>
      </Segment>
    </ModalWrapper>
  );
}
