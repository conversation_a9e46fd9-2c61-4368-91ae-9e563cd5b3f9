import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import '@testing-library/jest-dom';
import DocPrepareDashboard from '../DocPrepareDashboard';

// Mock all the complex dependencies
jest.mock('react-pdf/dist/umd/entry.webpack', () => ({
  Document: ({ children }) => <div data-testid="pdf-document">{children}</div>,
  Page: ({ pageNumber }) => <div data-testid={`pdf-page-${pageNumber}`}>Page {pageNumber}</div>
}));

jest.mock('react-rnd', () => ({
  Rnd: ({ children, ...props }) => <div data-testid="rnd-component" {...props}>{children}</div>
}));

jest.mock('../annots/AnnotDraggableField', () => {
  return function MockAnnotDraggableField({ annot }) {
    return <div data-testid={`draggable-field-${annot.uniqueId}`}>Draggable: {annot.type}</div>;
  };
});

jest.mock('../annots/AnnotOtherPartyField', () => {
  return function MockAnnotOtherPartyField({ annot }) {
    return <div data-testid={`other-party-field-${annot.uniqueId}`}>Other Party: {annot.signerRole} - {annot.type}</div>;
  };
});

jest.mock('../formFields/FormFieldSelector', () => {
  return function MockFormFieldSelector() {
    return <div data-testid="form-field-selector">Form Field</div>;
  };
});

jest.mock('../DocPrepareActionButtons', () => {
  return function MockDocPrepareActionButtons() {
    return <div data-testid="action-buttons">Action Buttons</div>;
  };
});

jest.mock('../signerList/DocPrepareSignerList', () => {
  return function MockDocPrepareSignerList() {
    return <div data-testid="signer-list">Signer List</div>;
  };
});

jest.mock('../annots/AnnotTypeMenu', () => {
  return function MockAnnotTypeMenu() {
    return <div data-testid="annot-type-menu">Annot Type Menu</div>;
  };
});

jest.mock('../annots/AnnotEditButtons', () => {
  return function MockAnnotEditButtons() {
    return <div data-testid="annot-edit-buttons">Edit Buttons</div>;
  };
});

// Mock firestore service
jest.mock('../../../../../app/firestore/firestoreService', () => ({
  updateDocInDb: jest.fn()
}));

// Mock util functions
jest.mock('../../../../../app/common/util/util', () => ({
  createSuggestedAnnots: jest.fn(),
  createAllSuggestedAnnots: jest.fn(),
  getAndSavePdfDimensions: jest.fn(),
  getFormFieldValues: jest.fn(() => [])
}));

const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

const mockState = {
  doc: {
    doc: {
      id: 'test-doc',
      docUrl: 'test-url',
      pdfBurnVersion: false,
      annotsInProgress: [
        {
          uniqueId: 'annot-1',
          page: 0,
          type: 'signature',
          signerRole: 'Buyer',
          agentsField: false,
          x: 100,
          y: 200,
          width: 150,
          height: 30
        },
        {
          uniqueId: 'annot-2',
          page: 0,
          type: 'signature',
          signerRole: 'Seller',
          agentsField: false,
          x: 300,
          y: 400,
          width: 150,
          height: 30
        }
      ]
    },
    docUrl: 'test-url'
  },
  annot: {
    annots: [
      {
        uniqueId: 'annot-1',
        page: 0,
        type: 'signature',
        signerRole: 'Buyer',
        agentsField: false,
        x: 100,
        y: 200,
        width: 150,
        height: 30
      },
      {
        uniqueId: 'annot-2',
        page: 0,
        type: 'signature',
        signerRole: 'Seller',
        agentsField: false,
        x: 300,
        y: 400,
        width: 150,
        height: 30
      }
    ],
    selectedAnnot: {},
    signerListDisplay: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
    ],
    editMode: 'client',
    pageScalePrepare: 1
  },
  transaction: {
    transaction: {
      agentRepresents: 'Buyer'
    },
    transClients: [{ role: 'Buyer', firstName: 'John', lastName: 'Doe' }],
    parties: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
    ]
  },
  profile: {
    currentUserProfile: {
      firstName: 'Test',
      lastName: 'User'
    }
  }
};

describe('DocPrepareDashboard - Other Party Fields', () => {
  test('renders signature fields for both parties when other party is in signer list', () => {
    const store = createMockStore(mockState);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Should render draggable field for buyer (current agent's client)
    expect(screen.getByTestId('draggable-field-annot-1')).toBeInTheDocument();

    // Should render draggable field for seller (other party client in signer list)
    expect(screen.getByTestId('draggable-field-annot-2')).toBeInTheDocument();
  });

  test('renders signature fields for both parties when agent represents seller', () => {
    const sellerAgentState = {
      ...mockState,
      transaction: {
        ...mockState.transaction,
        transaction: {
          agentRepresents: 'Seller'
        }
      }
    };

    const store = createMockStore(sellerAgentState);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Should render draggable field for seller (current agent's client)
    expect(screen.getByTestId('draggable-field-annot-2')).toBeInTheDocument();

    // Should render draggable field for buyer (other party client in signer list)
    expect(screen.getByTestId('draggable-field-annot-1')).toBeInTheDocument();
  });

  test('does not render other party fields when they are not in signer list', () => {
    const stateWithoutOtherPartyInSignerList = {
      ...mockState,
      annot: {
        ...mockState.annot,
        signerListDisplay: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
          // Seller not in signer list
        ]
      }
    };

    const store = createMockStore(stateWithoutOtherPartyInSignerList);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Should render draggable field for buyer (current agent's client)
    expect(screen.getByTestId('draggable-field-annot-1')).toBeInTheDocument();

    // Should still render seller annotation in the second section (other party not in signer list)
    expect(screen.getByTestId('draggable-field-annot-2')).toBeInTheDocument();
  });

  test('adds annotations for newly added parties', async () => {
    const { createAllSuggestedAnnots } = require('../../../../../app/common/util/util');
    const { updateDocInDb } = require('../../../../../app/firestore/firestoreService');

    // Mock the functions
    createAllSuggestedAnnots.mockReturnValue([
      { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
      { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 },
      { signerRole: 'Seller 2', type: 'signature', page: 0, x: 500, y: 600 } // New party annotation
    ]);

    const stateWithExistingAnnots = {
      ...mockState,
      doc: {
        doc: {
          id: 'test-doc',
          docUrl: 'test-url',
          pdfBurnVersion: false,
          annotsInProgressSuggestedAdded: true, // Already processed initial annotations
          annotsInProgressSuggested: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 },
            { signerRole: 'Seller 2', type: 'signature', page: 0, x: 500, y: 600 }
          ],
          annotsInProgress: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 }
            // Missing Seller 2 annotation (newly added party)
          ]
        },
        docUrl: 'test-url'
      },
      transaction: {
        ...mockState.transaction,
        parties: [
          ...mockState.transaction.parties,
          { role: 'Seller 2', firstName: 'New', lastName: 'Seller' } // Newly added party
        ]
      }
    };

    const store = createMockStore(stateWithExistingAnnots);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Wait for the effect to run
    await waitFor(() => {
      expect(updateDocInDb).toHaveBeenCalledWith(
        'test-doc',
        {
          annotsInProgress: expect.arrayContaining([
            expect.objectContaining({ signerRole: 'Seller 2', type: 'signature' })
          ])
        },
        false
      );
    });
  });
});
